import os
import socket
import json
import asyncio
import logging
from confluent_kafka import Consumer, KafkaException
from aws_msk_iam_sasl_signer import MSKAuthTokenProvider
from botocore.credentials import CredentialProvider, Credentials
from AlertDistributerEvent import AlertDistributerEvent
from AlertDistributerIoT import AlertDistributerIoT
from Producer import Producer
from db.RepositoryManager import RepositoryManager
from utils.AppSyncHandler import AppSyncHandler

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Environment variables
ACCESS_KEY = os.getenv("ACCESS_KEY", "********************")
SECRET_KEY = os.getenv("SECRET_KEY", "4XFYdQrDYfxDScDczKgTvs/IETy/Amkf/My4pXjM")
REGION = os.getenv("REGION", "us-east-2")
BOOTSTRAP_SERVERS = os.getenv("BOOTSTRAP_SERVERS","b-2-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-1-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198")


GROUP_ID = os.getenv("GROUP_ID", "test-groupaljkdsfhoiaufhcasuldfmhcasfuhcasc")

# Validate required environment variables
if not all([ACCESS_KEY, SECRET_KEY, BOOTSTRAP_SERVERS]):
    raise ValueError("Missing required environment variables: ACCESS_KEY, SECRET_KEY, or BOOTSTRAP_SERVERS")

class TestCredentialProvider(CredentialProvider):
    __test__ = False

    def load(self):
        return Credentials(access_key=ACCESS_KEY, secret_key=SECRET_KEY)

def oauth_cb(oauth_config):
    auth_token, expiry_ms = MSKAuthTokenProvider.generate_auth_token_from_credentials_provider(REGION, TestCredentialProvider())
    return auth_token, expiry_ms / 1000

class AlertDistributerHandler:
    RID_UI_TOPIC = os.getenv("RID_UI_TOPIC", "DETECTION_DEV")
    IOT_TOPIC = os.getenv("IOT_TOPIC", "IOT_DEV")
    repository_manager = RepositoryManager()
    producer = Producer()
    appSyncHandler = AppSyncHandler()
    def __init__(self):
        self.alertDistributer = AlertDistributerEvent(self.repository_manager, self.producer, self.appSyncHandler)
        self.alertDistributerIot = AlertDistributerIoT(self.repository_manager, self.producer, self.appSyncHandler)
        self.consumer = self.create_consumer()

    def create_consumer(self):
        """Create and configure Kafka consumer."""
        try:
            consumer = Consumer({
                'bootstrap.servers': BOOTSTRAP_SERVERS,
                'client.id': socket.gethostname(),
                'security.protocol': 'SASL_SSL',
                'sasl.mechanisms': 'OAUTHBEARER',
                'oauth_cb': oauth_cb,
                'group.id': GROUP_ID,
                'enable.auto.commit': True,
                'auto.offset.reset': 'latest',
            })
            consumer.subscribe([self.RID_UI_TOPIC, self.IOT_TOPIC])
            return consumer
        except KafkaException as e:
            logger.error(f"Failed to create Kafka consumer: {e}")
            raise

    async def process_event_message(self, msg):
        """Process incoming Kafka message asynchronously."""
        try:
            message = json.loads(msg.value().decode("utf-8"))
            await self.alertDistributer.disribute_event_notification(message)
        except Exception as e:
            logger.error(f"Error processing message: {e}\n{msg.value()}")

    async def process_iot_message(self, msg):
        """Process incoming Kafka message asynchronously."""
        try:
            message = json.loads(msg.value().decode("utf-8"))
            await self.alertDistributerIot.disribute_iot_notification(message)
        except Exception as e:
            logger.error(f"Error processing message: {e}\n{msg.value()}")

    async def consume_messages(self):
        print("listening to topics: ", self.IOT_TOPIC, self.RID_UI_TOPIC)
        """Consume messages asynchronously using asyncio."""
        try:
            while True:
                msg = await asyncio.to_thread(self.consumer.poll, 5)  # Poll in a non-blocking manner
                if msg is None:
                    continue
                if msg.error():
                    logger.error(f"Consumer error: {msg.error()}")
                    continue
                if(msg.topic() == self.RID_UI_TOPIC):
                    await self.process_event_message(msg)
                elif(msg.topic() == self.IOT_TOPIC):
                    await self.process_iot_message(msg)

        except asyncio.CancelledError:
            logger.info("Consumer task cancelled, shutting down gracefully.")
        finally:
            self.consumer.close()

async def main():
    """Main function to run the alert distributor."""
    logger.info("Starting Alert Distributor...")
    handler = AlertDistributerHandler()

    try:
        await handler.consume_messages()
    except Exception as e:
        logger.error(f"Unexpected error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
